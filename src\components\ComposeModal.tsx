import React, { useState } from 'react';
import { X, Image, Smile, MapPin, Calendar } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';

interface ComposeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPost?: (content: string) => Promise<void>;
}

export function ComposeModal({ isOpen, onClose, onPost }: ComposeModalProps) {
  const { user, profile } = useAuth();
  const [content, setContent] = useState('');
  const [isPosting, setIsPosting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim() || isPosting || !user) return;

    setIsPosting(true);
    try {
      if (onPost) {
        await onPost(content);
      } else {
        // Default post creation
        const { error } = await supabase
          .from('posts')
          .insert({
            user_id: user.id,
            content: content.trim(),
            likes_count: 0,
            reposts_count: 0,
            replies_count: 0
          });

        if (error) throw error;

        // Update user's posts count
        await supabase.rpc('increment_posts_count', { user_id: user.id });
      }

      setContent('');
      onClose();
    } catch (error) {
      console.error('Error posting:', error);
    }
    setIsPosting(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      handleSubmit(e);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-black border border-gray-800 rounded-2xl w-full max-w-lg mx-4"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-4 border-b border-gray-800">
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-white p-2 rounded-full hover:bg-gray-800"
              >
                <X size={20} />
              </button>
              <h2 className="text-lg font-semibold">إنشاء منشور</h2>
              <div></div>
            </div>

            <form onSubmit={handleSubmit} className="p-4">
              <div className="flex space-x-3 space-x-reverse">
                <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt="Profile"
                      className="w-12 h-12 rounded-full"
                    />
                  ) : (
                    <span className="text-lg font-semibold">
                      {profile?.username?.[0]?.toUpperCase() || 'U'}
                    </span>
                  )}
                </div>

                <div className="flex-1">
                  <textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="ما الذي يحدث؟"
                    className="w-full bg-transparent text-xl placeholder-gray-500 resize-none border-none outline-none"
                    rows={4}
                    maxLength={280}
                    autoFocus
                  />

                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center space-x-4">
                      <button
                        type="button"
                        className="text-blue-500 hover:bg-blue-500/10 p-2 rounded-full"
                      >
                        <Image size={20} />
                      </button>
                      <button
                        type="button"
                        className="text-blue-500 hover:bg-blue-500/10 p-2 rounded-full"
                      >
                        <Smile size={20} />
                      </button>
                      <button
                        type="button"
                        className="text-blue-500 hover:bg-blue-500/10 p-2 rounded-full"
                      >
                        <MapPin size={20} />
                      </button>
                      <button
                        type="button"
                        className="text-blue-500 hover:bg-blue-500/10 p-2 rounded-full"
                      >
                        <Calendar size={20} />
                      </button>
                    </div>

                    <div className="flex items-center space-x-3">
                      <span className={`text-sm ${content.length > 260 ? 'text-red-500' : 'text-gray-500'}`}>
                        {280 - content.length}
                      </span>
                      <motion.button
                        type="submit"
                        disabled={!content.trim() || isPosting}
                        className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 disabled:cursor-not-allowed text-white font-bold py-2 px-6 rounded-full transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {isPosting ? 'جاري النشر...' : 'نشر'}
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
