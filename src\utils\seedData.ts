import { faker } from '@faker-js/faker';
import { supabase } from '../lib/supabase';

// Predefined users for more realistic data
const sampleUsers = [
  {
    username: 'techguru',
    full_name: '<PERSON>',
    bio: 'Software Engineer | Tech enthusiast | Coffee lover ☕',
    verified: true,
  },
  {
    username: 'design<PERSON><PERSON>',
    full_name: '<PERSON>',
    bio: 'UI/UX Designer | Creating beautiful experiences',
    verified: false,
  },
  {
    username: 'startup_founder',
    full_name: '<PERSON>',
    bio: 'Entrepreneur | Building the future | 🚀',
    verified: true,
  },
  {
    username: 'codemaster',
    full_name: '<PERSON>',
    bio: 'Full-stack developer | Open source contributor',
    verified: false,
  },
  {
    username: 'data_scientist',
    full_name: 'Dr. <PERSON>',
    bio: 'Data Scientist | AI researcher | PhD in CS',
    verified: true,
  },
];

const samplePosts = [
  "Just shipped a new feature! The feeling never gets old 🚀",
  "Coffee + code = perfect morning ☕💻",
  "Working on something exciting. Can't wait to share it with everyone!",
  "The best code is no code at all. Sometimes the simplest solution is the best.",
  "Reading about the latest developments in AI. The future is fascinating!",
  "Design is not just what it looks like and feels like. Design is how it works.",
  "Building in public is scary but so rewarding. Thanks for all the support!",
  "Remember: Done is better than perfect. Ship it! 📦",
  "Great meeting with the team today. Love working with passionate people!",
  "Learning something new every day. That's what keeps this job exciting.",
  "User feedback is gold. Always listen to your users 👂",
  "Late night coding session. When you're in the zone, time flies ⏰",
  "Excited to speak at the upcoming tech conference! See you there 🎤",
  "Open source is amazing. Thanks to all the contributors making the world better!",
  "Just finished a great book on system design. Highly recommend it! 📚",
];

export async function seedDatabase() {
  try {
    // Check if we already have seed data
    const { data: existingPosts } = await supabase
      .from('posts')
      .select('id')
      .limit(1);

    if (existingPosts && existingPosts.length > 0) {
      console.log('Seed data already exists');
      return;
    }

    console.log('Creating seed data...');

    // Create sample profiles (these would be created via auth signup in real app)
    const profiles = sampleUsers.map(user => ({
      id: faker.string.uuid(),
      username: user.username,
      full_name: user.full_name,
      bio: user.bio,
      verified: user.verified,
      followers_count: faker.number.int({ min: 100, max: 10000 }),
      following_count: faker.number.int({ min: 50, max: 1000 }),
      posts_count: faker.number.int({ min: 10, max: 100 }),
      created_at: faker.date.past({ years: 2 }).toISOString(),
      updated_at: new Date().toISOString(),
    }));

    // Insert profiles
    const { error: profileError } = await supabase
      .from('profiles')
      .insert(profiles);

    if (profileError) {
      console.error('Error creating profiles:', profileError);
      return;
    }

    // Create sample posts
    const posts = [];
    profiles.forEach(profile => {
      const postCount = faker.number.int({ min: 3, max: 8 });
      for (let i = 0; i < postCount; i++) {
        posts.push({
          id: faker.string.uuid(),
          user_id: profile.id,
          content: faker.helpers.arrayElement(samplePosts),
          likes_count: faker.number.int({ min: 0, max: 500 }),
          reposts_count: faker.number.int({ min: 0, max: 50 }),
          replies_count: faker.number.int({ min: 0, max: 25 }),
          created_at: faker.date.recent({ days: 30 }).toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    });

    // Insert posts
    const { error: postsError } = await supabase
      .from('posts')
      .insert(posts);

    if (postsError) {
      console.error('Error creating posts:', postsError);
      return;
    }

    // Create some sample likes
    const likes = [];
    posts.forEach(post => {
      const likesCount = faker.number.int({ min: 0, max: Math.min(10, profiles.length) });
      const likers = faker.helpers.arrayElements(profiles, likesCount);
      likers.forEach(liker => {
        likes.push({
          id: faker.string.uuid(),
          user_id: liker.id,
          post_id: post.id,
          created_at: faker.date.recent({ days: 30 }).toISOString(),
        });
      });
    });

    const { error: likesError } = await supabase
      .from('likes')
      .insert(likes);

    if (likesError) {
      console.error('Error creating likes:', likesError);
      return;
    }

    console.log('Seed data created successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}
