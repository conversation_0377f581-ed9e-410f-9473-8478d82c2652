/*
# Add RPC Functions for Post and User Operations

This migration adds necessary RPC (Remote Procedure Call) functions for handling
post likes counting and user post counting operations safely and consistently.

## Query Description:
This operation adds utility functions to maintain data consistency for social media
interactions. These functions ensure atomic operations for incrementing/decrementing
counters and are essential for proper application functionality.

## Metadata:
- Schema-Category: "Safe"
- Impact-Level: "Low"
- Requires-Backup: false
- Reversible: true

## Structure Details:
- increment_likes_count: Function to safely increment post likes
- decrement_likes_count: Function to safely decrement post likes  
- increment_posts_count: Function to safely increment user post count

## Security Implications:
- RLS Status: Functions respect existing RLS policies
- Policy Changes: No
- Auth Requirements: Functions require authenticated users

## Performance Impact:
- Indexes: No changes to existing indexes
- Triggers: No triggers modified
- Estimated Impact: Minimal performance impact, improves consistency
*/

-- Function to increment likes count for a post
CREATE OR REPLACE FUNCTION increment_likes_count(post_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE posts 
  SET likes_count = likes_count + 1, updated_at = NOW()
  WHERE id = post_id;
END;
$$;

-- Function to decrement likes count for a post
CREATE OR REPLACE FUNCTION decrement_likes_count(post_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE posts 
  SET likes_count = GREATEST(likes_count - 1, 0), updated_at = NOW()
  WHERE id = post_id;
END;
$$;

-- Function to increment posts count for a user
CREATE OR REPLACE FUNCTION increment_posts_count(user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles 
  SET posts_count = posts_count + 1, updated_at = NOW()
  WHERE id = user_id;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION increment_likes_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_likes_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_posts_count(UUID) TO authenticated;
