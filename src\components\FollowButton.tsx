import React, { useState, useEffect } from 'react';
import { UserPlus, UserMinus } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface FollowButtonProps {
  userId: string;
  onFollowChange?: (isFollowing: boolean) => void;
  className?: string;
}

export function FollowButton({ userId, onFollowChange, className = '' }: FollowButtonProps) {
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user && userId && user.id !== userId) {
      checkFollowStatus();
    }
  }, [user, userId]);

  const checkFollowStatus = async () => {
    if (!user || !userId) return;

    try {
      const { data, error } = await supabase
        .from('follows')
        .select('id')
        .eq('follower_id', user.id)
        .eq('following_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      setIsFollowing(!!data);
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  };

  const toggleFollow = async () => {
    if (!user || !userId || loading) return;

    try {
      setLoading(true);

      if (isFollowing) {
        // Unfollow
        const { error } = await supabase
          .from('follows')
          .delete()
          .eq('follower_id', user.id)
          .eq('following_id', userId);

        if (error) throw error;

        // Update counters
        await supabase.rpc('decrement_followers_count', { user_id: userId });
        await supabase.rpc('decrement_following_count', { user_id: user.id });

        setIsFollowing(false);
        if (onFollowChange) onFollowChange(false);
      } else {
        // Follow
        const { error } = await supabase
          .from('follows')
          .insert({
            follower_id: user.id,
            following_id: userId
          });

        if (error) throw error;

        // Update counters
        await supabase.rpc('increment_followers_count', { user_id: userId });
        await supabase.rpc('increment_following_count', { user_id: user.id });

        // Create notification
        await supabase
          .from('notifications')
          .insert({
            user_id: userId,
            type: 'follow',
            title: 'متابع جديد',
            message: 'بدأ بمتابعتك',
            related_user_id: user.id
          });

        setIsFollowing(true);
        if (onFollowChange) onFollowChange(true);
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
    } finally {
      setLoading(false);
    }
  };

  // Don't show button for own profile
  if (!user || !userId || user.id === userId) {
    return null;
  }

  return (
    <button
      onClick={toggleFollow}
      disabled={loading}
      className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
        isFollowing
          ? 'bg-gray-800 text-white hover:bg-red-600 hover:text-white'
          : 'bg-blue-600 text-white hover:bg-blue-700'
      } ${className}`}
    >
      {loading ? (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
      ) : isFollowing ? (
        <>
          <UserMinus className="h-4 w-4" />
          <span>إلغاء المتابعة</span>
        </>
      ) : (
        <>
          <UserPlus className="h-4 w-4" />
          <span>متابعة</span>
        </>
      )}
    </button>
  );
}
