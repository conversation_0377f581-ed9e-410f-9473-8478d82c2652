import React from 'react';
import { Home, Search, Bell, Mail, Bookmark, User, MoreHorizontal, Plus } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface LayoutProps {
  children: React.ReactNode;
  onComposeClick: () => void;
}

export function Layout({ children, onComposeClick }: LayoutProps) {
  const { profile, signOut } = useAuth();

  const sidebarItems = [
    { icon: Home, label: 'Home', active: true },
    { icon: Search, label: 'Explore' },
    { icon: Bell, label: 'Notifications' },
    { icon: Mail, label: 'Messages' },
    { icon: Bookmark, label: 'Bookmarks' },
    { icon: User, label: 'Profile' },
    { icon: MoreHorizontal, label: 'More' },
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-6xl mx-auto flex">
        {/* Sidebar */}
        <div className="w-64 fixed h-full border-r border-gray-800 p-4">
          {/* Logo */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold">Forvgo</h1>
          </div>

          {/* Navigation */}
          <nav className="space-y-2 mb-8">
            {sidebarItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.label}
                  className={`flex items-center space-x-3 w-full p-3 rounded-full transition-colors ${
                    item.active
                      ? 'bg-blue-500/10 text-blue-500'
                      : 'hover:bg-gray-800'
                  }`}
                >
                  <Icon size={24} />
                  <span className="text-xl hidden xl:block">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Compose Button */}
          <button
            onClick={onComposeClick}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-8 rounded-full w-full xl:w-auto transition-colors mb-8"
          >
            <Plus size={24} className="xl:hidden" />
            <span className="hidden xl:block">Post</span>
          </button>

          {/* User Profile */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="flex items-center justify-between p-3 hover:bg-gray-800 rounded-full cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt="Profile"
                      className="w-10 h-10 rounded-full"
                    />
                  ) : (
                    <User size={20} />
                  )}
                </div>
                <div className="hidden xl:block">
                  <p className="font-semibold">{profile?.full_name || profile?.username}</p>
                  <p className="text-gray-500 text-sm">@{profile?.username}</p>
                </div>
              </div>
              <button
                onClick={signOut}
                className="hidden xl:block text-gray-500 hover:text-white"
              >
                <MoreHorizontal size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 ml-64">
          {children}
        </div>
      </div>
    </div>
  );
}
