import React from 'react';
import { Sidebar } from './Sidebar';
import { RightSidebar } from './RightSidebar';

interface LayoutProps {
  children: React.ReactNode;
  showRightSidebar?: boolean;
}

export function Layout({ children, showRightSidebar = true }: LayoutProps) {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="flex max-w-7xl mx-auto">
        <Sidebar />

        {/* Main Content */}
        <div className="flex-1 ml-64">
          {children}
        </div>

        {/* Right Sidebar */}
        {showRightSidebar && (
          <div className="hidden lg:block">
            <RightSidebar />
          </div>
        )}
      </div>
    </div>
  );
}
