import React from 'react';
import { Search, TrendingUp } from 'lucide-react';
import { SuggestedUsers } from './SuggestedUsers';

export function RightSidebar() {
  const trendingTopics = [
    { tag: '#تقنية', posts: '125K منشور' },
    { tag: '#برمجة', posts: '89K منشور' },
    { tag: '#ذكي_اصطناعي', posts: '67K منشور' },
    { tag: '#تطوير_ويب', posts: '45K منشور' },
    { tag: '#موبايل', posts: '32K منشور' },
  ];

  return (
    <div className="w-80 p-4 space-y-4">
      {/* Search */}
      <div className="sticky top-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في Forvgo"
            className="w-full bg-gray-900 border border-gray-700 rounded-full py-3 pl-12 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
          />
        </div>
      </div>

      {/* Trending */}
      <div className="bg-gray-900 rounded-2xl p-4">
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <TrendingUp className="h-5 w-5 ml-2" />
          الأكثر رواجاً
        </h2>
        <div className="space-y-3">
          {trendingTopics.map((topic, index) => (
            <div key={topic.tag} className="hover:bg-gray-800 p-2 rounded-lg cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">الأكثر رواجاً في التقنية</p>
                  <p className="font-semibold text-white">{topic.tag}</p>
                  <p className="text-gray-400 text-sm">{topic.posts}</p>
                </div>
                <span className="text-gray-500 text-sm">#{index + 1}</span>
              </div>
            </div>
          ))}
        </div>
        <button className="text-blue-400 hover:text-blue-300 text-sm mt-3 transition-colors">
          عرض المزيد
        </button>
      </div>

      {/* Suggested Users */}
      <SuggestedUsers />

      {/* Footer */}
      <div className="text-gray-500 text-sm space-y-2">
        <div className="flex flex-wrap gap-2">
          <a href="#" className="hover:text-gray-300 transition-colors">شروط الخدمة</a>
          <a href="#" className="hover:text-gray-300 transition-colors">سياسة الخصوصية</a>
          <a href="#" className="hover:text-gray-300 transition-colors">سياسة ملفات تعريف الارتباط</a>
        </div>
        <div className="flex flex-wrap gap-2">
          <a href="#" className="hover:text-gray-300 transition-colors">إمكانية الوصول</a>
          <a href="#" className="hover:text-gray-300 transition-colors">معلومات الإعلانات</a>
          <a href="#" className="hover:text-gray-300 transition-colors">المزيد</a>
        </div>
        <p>© 2024 Forvgo Corp.</p>
      </div>
    </div>
  );
}
