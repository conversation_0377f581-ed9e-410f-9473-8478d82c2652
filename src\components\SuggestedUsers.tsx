import React, { useState, useEffect } from 'react';
import { User } from 'lucide-react';
import { supabase, Profile } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { FollowButton } from './FollowButton';

export function SuggestedUsers() {
  const { user } = useAuth();
  const [suggestedUsers, setSuggestedUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchSuggestedUsers();
    }
  }, [user]);

  const fetchSuggestedUsers = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Get users that the current user is not following
      const { data: followingData } = await supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', user.id);

      const followingIds = followingData?.map(f => f.following_id) || [];
      followingIds.push(user.id); // Exclude self

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .not('id', 'in', `(${followingIds.join(',')})`)
        .order('followers_count', { ascending: false })
        .limit(5);

      if (error) throw error;
      setSuggestedUsers(data || []);
    } catch (error) {
      console.error('Error fetching suggested users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (userId: string, isFollowing: boolean) => {
    if (!isFollowing) {
      // Remove from suggested users when unfollowed
      setSuggestedUsers(prev => prev.filter(u => u.id !== userId));
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-900 rounded-2xl p-4">
        <h2 className="text-xl font-bold mb-4">من تتابع</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3 space-x-reverse animate-pulse">
              <div className="w-10 h-10 bg-gray-700 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-700 rounded w-24 mb-1"></div>
                <div className="h-3 bg-gray-700 rounded w-16"></div>
              </div>
              <div className="w-16 h-8 bg-gray-700 rounded-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (suggestedUsers.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-900 rounded-2xl p-4">
      <h2 className="text-xl font-bold mb-4">من تتابع</h2>
      <div className="space-y-3">
        {suggestedUsers.map((suggestedUser) => (
          <div key={suggestedUser.id} className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse flex-1 min-w-0">
              {suggestedUser.avatar_url ? (
                <img
                  src={suggestedUser.avatar_url}
                  alt={suggestedUser.full_name || suggestedUser.username}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-gray-300" />
                </div>
              )}
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-1 space-x-reverse">
                  <p className="font-semibold text-white truncate">
                    {suggestedUser.full_name || suggestedUser.username}
                  </p>
                  {suggestedUser.verified && (
                    <span className="text-blue-500 text-sm">✓</span>
                  )}
                </div>
                <p className="text-gray-400 text-sm truncate">
                  @{suggestedUser.username}
                </p>
                <p className="text-gray-500 text-xs">
                  {suggestedUser.followers_count} متابع
                </p>
              </div>
            </div>
            
            <FollowButton
              userId={suggestedUser.id}
              onFollowChange={(isFollowing) => handleFollowChange(suggestedUser.id, isFollowing)}
              className="text-sm px-3 py-1"
            />
          </div>
        ))}
      </div>
      
      <button className="text-blue-400 hover:text-blue-300 text-sm mt-3 transition-colors">
        عرض المزيد
      </button>
    </div>
  );
}
