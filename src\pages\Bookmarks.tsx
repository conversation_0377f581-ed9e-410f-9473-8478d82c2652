import React, { useState, useEffect } from 'react';
import { Bookmark, Trash2 } from 'lucide-react';
import { Layout } from '../components/Layout';
import { PostCard } from '../components/PostCard';
import { supabase, Post, Bookmark as BookmarkType } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export function Bookmarks() {
  const { user } = useAuth();
  const [bookmarks, setBookmarks] = useState<(BookmarkType & { posts: Post })[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchBookmarks();
    }
  }, [user]);

  const fetchBookmarks = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('bookmarks')
        .select(`
          *,
          posts (
            *,
            profiles (
              id,
              username,
              full_name,
              avatar_url,
              verified
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBookmarks(data || []);
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeBookmark = async (bookmarkId: string) => {
    try {
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('id', bookmarkId);

      if (error) throw error;
      
      setBookmarks(bookmarks.filter(bookmark => bookmark.id !== bookmarkId));
    } catch (error) {
      console.error('Error removing bookmark:', error);
    }
  };

  const clearAllBookmarks = async () => {
    if (!user) return;
    
    const confirmed = window.confirm('هل أنت متأكد من حذف جميع المفضلة؟');
    if (!confirmed) return;

    try {
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;
      
      setBookmarks([]);
    } catch (error) {
      console.error('Error clearing bookmarks:', error);
    }
  };

  return (
    <Layout>
      <div className="max-w-2xl mx-auto border-x border-gray-800 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold flex items-center">
                <Bookmark className="h-6 w-6 ml-2" />
                المفضلة
              </h1>
              <p className="text-gray-400 text-sm mt-1">
                {bookmarks.length} منشور محفوظ
              </p>
            </div>
            
            {bookmarks.length > 0 && (
              <button
                onClick={clearAllBookmarks}
                className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-full transition-colors"
              >
                <Trash2 className="h-4 w-4" />
                <span className="text-sm">حذف الكل</span>
              </button>
            )}
          </div>
        </div>

        {/* Bookmarks */}
        <div>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : bookmarks.length === 0 ? (
            <div className="text-center py-16">
              <Bookmark className="h-16 w-16 text-gray-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-300 mb-2">
                لا توجد مفضلة محفوظة
              </h2>
              <p className="text-gray-500 max-w-sm mx-auto">
                عندما تحفظ منشورات، ستظهر هنا لتتمكن من الوصول إليها بسهولة لاحقاً
              </p>
            </div>
          ) : (
            bookmarks.map((bookmark) => (
              <div key={bookmark.id} className="relative group">
                <PostCard post={bookmark.posts} />
                
                {/* Remove Bookmark Button */}
                <button
                  onClick={() => removeBookmark(bookmark.id)}
                  className="absolute top-4 left-4 opacity-0 group-hover:opacity-100 p-2 bg-red-600 hover:bg-red-700 text-white rounded-full transition-all duration-200"
                  title="إزالة من المفضلة"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))
          )}
        </div>

        {/* Tips */}
        {bookmarks.length === 0 && !loading && (
          <div className="p-4 m-4 bg-gray-900 rounded-lg border border-gray-800">
            <h3 className="font-semibold text-white mb-2">نصائح للاستفادة من المفضلة:</h3>
            <ul className="text-gray-400 text-sm space-y-1">
              <li>• احفظ المنشورات المهمة للرجوع إليها لاحقاً</li>
              <li>• استخدم المفضلة لحفظ المقالات والموارد المفيدة</li>
              <li>• يمكنك إزالة المنشورات من المفضلة في أي وقت</li>
            </ul>
          </div>
        )}
      </div>
    </Layout>
  );
}
