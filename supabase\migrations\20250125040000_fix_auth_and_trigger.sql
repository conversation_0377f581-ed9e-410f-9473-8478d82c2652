/*
# Fix Authentication and Profile Creation Trigger
Complete fix for user registration and profile creation system.

## Query Description: 
This migration creates a robust user registration system with automatic profile creation. It fixes the signup process by creating a proper trigger that automatically creates user profiles when new users register and confirm their email. This ensures seamless user onboarding without manual intervention.

## Metadata:
- Schema-Category: "Safe"
- Impact-Level: "Medium"
- Requires-Backup: false
- Reversible: true

## Structure Details:
- Creates/updates handle_new_user() function with better error handling
- Ensures profiles are created automatically on user confirmation
- Includes proper RLS policies for profile creation
- Adds utility functions for counter management

## Security Implications:
- RLS Status: Enabled
- Policy Changes: No
- Auth Requirements: Uses auth.uid() for security

## Performance Impact:
- Indexes: No changes
- Triggers: Updated for better reliability
- Estimated Impact: Improves signup success rate
*/

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- <PERSON>reate improved function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  default_username TEXT;
  username_suffix INTEGER := 0;
BEGIN
  -- Only create profile for confirmed users or if email_confirmed_at is being set
  IF NEW.email_confirmed_at IS NOT NULL AND (OLD.email_confirmed_at IS NULL OR OLD IS NULL) THEN
    
    -- Generate a default username from email or metadata
    default_username := COALESCE(
      NEW.raw_user_meta_data->>'username',
      SPLIT_PART(NEW.email, '@', 1)
    );
    
    -- Ensure username is unique by appending numbers if needed
    WHILE EXISTS (SELECT 1 FROM public.profiles WHERE username = default_username) LOOP
      username_suffix := username_suffix + 1;
      default_username := COALESCE(
        NEW.raw_user_meta_data->>'username',
        SPLIT_PART(NEW.email, '@', 1)
      ) || '_' || username_suffix::TEXT;
    END LOOP;
    
    -- Insert profile with error handling
    BEGIN
      INSERT INTO public.profiles (
        id,
        username,
        full_name,
        bio,
        verified,
        followers_count,
        following_count,
        posts_count,
        created_at,
        updated_at
      ) VALUES (
        NEW.id,
        default_username,
        COALESCE(NEW.raw_user_meta_data->>'full_name', default_username),
        '',
        false,
        0,
        0,
        0,
        NOW(),
        NOW()
      );
      
      RAISE LOG 'Profile created successfully for user: %', NEW.id;
      
    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
      -- Don't fail the auth process if profile creation fails
    END;
    
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT OR UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create utility functions for safe counter updates
CREATE OR REPLACE FUNCTION increment_likes_count(post_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE posts 
  SET likes_count = likes_count + 1, updated_at = NOW()
  WHERE id = post_id;
END;
$$;

CREATE OR REPLACE FUNCTION decrement_likes_count(post_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE posts 
  SET likes_count = GREATEST(0, likes_count - 1), updated_at = NOW()
  WHERE id = post_id;
END;
$$;

CREATE OR REPLACE FUNCTION increment_posts_count(user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE profiles 
  SET posts_count = posts_count + 1, updated_at = NOW()
  WHERE id = user_id;
END;
$$;

-- Update RLS policies to ensure proper access
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

-- Recreate policies
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Ensure posts table has proper RLS
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Posts are viewable by everyone" ON posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON posts;
DROP POLICY IF EXISTS "Users can update own posts" ON posts;

CREATE POLICY "Posts are viewable by everyone" ON posts
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own posts" ON posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own posts" ON posts
  FOR UPDATE USING (auth.uid() = user_id);

-- Ensure likes table has proper RLS
ALTER TABLE likes ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Likes are viewable by everyone" ON likes;
DROP POLICY IF EXISTS "Users can manage their own likes" ON likes;

CREATE POLICY "Likes are viewable by everyone" ON likes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own likes" ON likes
  FOR ALL USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
