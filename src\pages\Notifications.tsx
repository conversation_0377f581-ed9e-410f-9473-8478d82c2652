import React, { useState, useEffect } from 'react';
import { Heart, MessageCircle, UserPlus, Repeat, AtSign } from 'lucide-react';
import { Layout } from '../components/Layout';
import { supabase, Notification } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export function Notifications() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'mentions'>('all');

  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user, activeTab]);

  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      let query = supabase
        .from('notifications')
        .select(`
          *,
          profiles!notifications_related_user_id_fkey (
            id,
            username,
            full_name,
            avatar_url,
            verified
          ),
          posts (
            id,
            content
          )
        `)
        .eq('user_id', user.id);

      if (activeTab === 'mentions') {
        query = query.eq('type', 'mention');
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      setNotifications(data || []);

      // Mark notifications as read
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);

    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <Heart className="h-6 w-6 text-red-500" />;
      case 'comment':
        return <MessageCircle className="h-6 w-6 text-blue-500" />;
      case 'follow':
        return <UserPlus className="h-6 w-6 text-green-500" />;
      case 'repost':
        return <Repeat className="h-6 w-6 text-green-500" />;
      case 'mention':
        return <AtSign className="h-6 w-6 text-blue-500" />;
      default:
        return <Heart className="h-6 w-6 text-gray-500" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}د`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}س`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}ي`;
    return date.toLocaleDateString('ar');
  };

  return (
    <Layout>
      <div className="max-w-2xl mx-auto border-x border-gray-800 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 p-4">
          <h1 className="text-xl font-bold mb-4">الإشعارات</h1>
          
          {/* Tabs */}
          <div className="flex space-x-4 space-x-reverse">
            <button
              onClick={() => setActiveTab('all')}
              className={`px-4 py-2 rounded-full transition-colors ${
                activeTab === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              الكل
            </button>
            <button
              onClick={() => setActiveTab('mentions')}
              className={`px-4 py-2 rounded-full transition-colors ${
                activeTab === 'mentions'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              الإشارات
            </button>
          </div>
        </div>

        {/* Notifications */}
        <div>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <p>لا توجد إشعارات</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-b border-gray-800 hover:bg-gray-900/50 transition-colors ${
                  !notification.read ? 'bg-blue-500/5' : ''
                }`}
              >
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="flex-shrink-0">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {notification.profiles?.avatar_url ? (
                        <img
                          src={notification.profiles.avatar_url}
                          alt={notification.profiles.full_name || notification.profiles.username}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                      )}
                      
                      <div className="flex-1">
                        <p className="text-white">
                          <span className="font-semibold">
                            {notification.profiles?.full_name || notification.profiles?.username}
                          </span>
                          <span className="text-gray-400 mr-2">
                            {notification.message}
                          </span>
                        </p>
                        
                        {notification.posts && (
                          <p className="text-gray-400 text-sm mt-1 truncate">
                            {notification.posts.content}
                          </p>
                        )}
                        
                        <p className="text-gray-500 text-sm mt-1">
                          {formatTimeAgo(notification.created_at)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </Layout>
  );
}
