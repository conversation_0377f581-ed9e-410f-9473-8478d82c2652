# Forvgo - منصة التواصل الاجتماعي

منصة تواصل اجتماعي حديثة مبنية بـ React و TypeScript مع Supabase كقاعدة بيانات.

## المميزات

### 🏠 الصفحات الرئيسية
- **الرئيسية**: عرض المنشورات من المستخدمين المتابعين
- **استكشاف**: البحث واستكشاف المحتوى الشائع
- **الإشعارات**: تتبع التفاعلات والمتابعات الجديدة
- **الرسائل**: نظام رسائل خاصة بين المستخدمين
- **المفضلة**: حفظ المنشورات المهمة
- **الملف الشخصي**: عرض وتعديل معلومات المستخدم

### 🚀 المميزات التفاعلية
- **نظام المتابعة**: متابعة وإلغاء متابعة المستخدمين
- **التفاعل مع المنشورات**: إعجاب، تعليق، مشاركة
- **نظام الإشعارات**: إشعارات فورية للتفاعلات
- **البحث**: البحث في المنشورات والمستخدمين
- **المفضلة**: حفظ المنشورات للمراجعة لاحقاً

### 🎨 التصميم والواجهة
- **تصميم مظلم**: واجهة مستخدم أنيقة ومريحة للعين
- **رسوم متحركة**: تفاعلات سلسة باستخدام Framer Motion
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **واجهة عربية**: دعم كامل للغة العربية

## التقنيات المستخدمة

- **Frontend**: React 19, TypeScript, Vite
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Icons**: Lucide React
- **Routing**: React Router DOM

## التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn
- حساب Supabase

### خطوات التثبيت

1. **تثبيت التبعيات**
```bash
npm install
```

2. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
```

أضف معلومات Supabase في ملف `.env`:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

3. **إعداد قاعدة البيانات**
قم بتشغيل migrations في Supabase Dashboard أو CLI

4. **تشغيل المشروع**
```bash
npm run dev
```

## بنية المشروع

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
├── pages/              # صفحات التطبيق
├── contexts/           # React Contexts
├── lib/               # المكتبات والإعدادات
└── utils/             # الأدوات المساعدة
```

## قاعدة البيانات

### الجداول الرئيسية
- `profiles`: معلومات المستخدمين
- `posts`: المنشورات
- `likes`: الإعجابات
- `follows`: المتابعات
- `comments`: التعليقات
- `notifications`: الإشعارات
- `messages`: الرسائل الخاصة
- `bookmarks`: المفضلة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.