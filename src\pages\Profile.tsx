import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Link as LinkIcon, Edit } from 'lucide-react';
import { Layout } from '../components/Layout';
import { PostCard } from '../components/PostCard';
import { FollowButton } from '../components/FollowButton';
import { supabase, Post, Profile as ProfileType } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { useParams } from 'react-router-dom';

export function Profile() {
  const { user, profile: currentUserProfile } = useAuth();
  const { username } = useParams<{ username?: string }>();
  const [profile, setProfile] = useState<ProfileType | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'posts' | 'replies' | 'media'>('posts');

  const isOwnProfile = !username || username === currentUserProfile?.username;
  const targetUsername = username || currentUserProfile?.username;

  useEffect(() => {
    if (targetUsername) {
      fetchProfile();
      fetchPosts();
    }
  }, [targetUsername, user, activeTab]);

  const fetchProfile = async () => {
    if (!targetUsername) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('username', targetUsername)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const fetchPosts = async () => {
    if (!targetUsername) return;

    try {
      setLoading(true);
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', targetUsername)
        .single();

      if (!profileData) return;

      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            verified
          )
        `)
        .eq('user_id', profileData.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPosts(data || []);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing: boolean) => {
    if (!profile) return;

    setProfile(prev => prev ? {
      ...prev,
      followers_count: isFollowing
        ? prev.followers_count + 1
        : Math.max(0, prev.followers_count - 1)
    } : null);
  };

  const formatJoinDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', { year: 'numeric', month: 'long' });
  };

  if (!profile) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto border-x border-gray-800 min-h-screen">
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto border-x border-gray-800 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 p-4">
          <div>
            <h1 className="text-xl font-bold">{profile.full_name || profile.username}</h1>
            <p className="text-gray-400 text-sm">{profile.posts_count} منشور</p>
          </div>
        </div>

        {/* Profile Info */}
        <div className="p-4">
          {/* Cover Photo */}
          <div className="h-48 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mb-4"></div>

          {/* Profile Picture and Actions */}
          <div className="flex items-end justify-between mb-4">
            <div className="relative -mt-16">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || profile.username}
                  className="w-32 h-32 rounded-full border-4 border-black object-cover"
                />
              ) : (
                <div className="w-32 h-32 bg-gray-600 rounded-full border-4 border-black flex items-center justify-center">
                  <span className="text-4xl font-bold text-white">
                    {(profile.full_name || profile.username).charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            <div className="flex space-x-2 space-x-reverse">
              {isOwnProfile ? (
                <button className="flex items-center space-x-2 space-x-reverse px-4 py-2 border border-gray-600 text-white rounded-full hover:bg-gray-800 transition-colors">
                  <Edit className="h-4 w-4" />
                  <span>تعديل الملف الشخصي</span>
                </button>
              ) : (
                <FollowButton
                  userId={profile.id}
                  onFollowChange={handleFollowChange}
                />
              )}
            </div>
          </div>

          {/* Profile Details */}
          <div className="space-y-3">
            <div>
              <h2 className="text-xl font-bold text-white">
                {profile.full_name || profile.username}
                {profile.verified && (
                  <span className="text-blue-500 ml-1">✓</span>
                )}
              </h2>
              <p className="text-gray-400">@{profile.username}</p>
            </div>

            {profile.bio && (
              <p className="text-white">{profile.bio}</p>
            )}

            <div className="flex flex-wrap items-center gap-4 text-gray-400 text-sm">
              {profile.location && (
                <div className="flex items-center space-x-1 space-x-reverse">
                  <MapPin className="h-4 w-4" />
                  <span>{profile.location}</span>
                </div>
              )}
              
              {profile.website && (
                <div className="flex items-center space-x-1 space-x-reverse">
                  <LinkIcon className="h-4 w-4" />
                  <a
                    href={profile.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:underline"
                  >
                    {profile.website}
                  </a>
                </div>
              )}
              
              <div className="flex items-center space-x-1 space-x-reverse">
                <Calendar className="h-4 w-4" />
                <span>انضم في {formatJoinDate(profile.created_at)}</span>
              </div>
            </div>

            <div className="flex space-x-6 space-x-reverse text-sm">
              <div>
                <span className="font-bold text-white">{profile.following_count}</span>
                <span className="text-gray-400 mr-1">يتابع</span>
              </div>
              <div>
                <span className="font-bold text-white">{profile.followers_count}</span>
                <span className="text-gray-400 mr-1">متابع</span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-800">
          <div className="flex">
            {[
              { key: 'posts', label: 'المنشورات' },
              { key: 'replies', label: 'الردود' },
              { key: 'media', label: 'الوسائط' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 py-4 text-center transition-colors ${
                  activeTab === tab.key
                    ? 'text-white border-b-2 border-blue-500'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Posts */}
        <div>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <p>لا توجد منشورات</p>
            </div>
          ) : (
            posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))
          )}
        </div>
      </div>
    </Layout>
  );
}
