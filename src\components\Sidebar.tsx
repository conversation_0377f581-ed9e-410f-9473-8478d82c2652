import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  Search,
  Bell,
  Mail,
  Bookmark,
  User,
  LogOut,
  MoreHorizontal
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { ComposeModal } from './ComposeModal';

const navigationItems = [
  { name: 'الرئيسية', href: '/', icon: Home },
  { name: 'استكشاف', href: '/explore', icon: Search },
  { name: 'الإشعارات', href: '/notifications', icon: Bell },
  { name: 'الرسائل', href: '/messages', icon: Mail },
  { name: 'المفضلة', href: '/bookmarks', icon: Bookmark },
  { name: 'الملف الشخصي', href: '/profile', icon: User },
];

export function Sidebar() {
  const location = useLocation();
  const { user, signOut, profile } = useAuth();
  const [isComposeOpen, setIsComposeOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-black border-r border-gray-800 flex flex-col">
      {/* Logo */}
      <div className="p-6">
        <h1 className="text-2xl font-bold text-white">Forvgo</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.href;
            
            return (
              <motion.li
                key={item.name}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to={item.href}
                  className={`flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-full transition-colors ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-900 hover:text-white'
                  }`}
                >
                  <Icon className="h-6 w-6" />
                  <span className="text-lg font-medium">{item.name}</span>
                </Link>
              </motion.li>
            );
          })}
        </ul>

        {/* Tweet Button */}
        <div className="mt-8">
          <motion.button
            onClick={() => setIsComposeOpen(true)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            تغريد
          </motion.button>
        </div>
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
              {profile?.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || profile.username}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <User className="h-6 w-6 text-gray-300" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-white font-medium truncate">
                {profile?.full_name || profile?.username || 'المستخدم'}
              </p>
              <p className="text-gray-400 text-sm truncate">
                @{profile?.username || 'user'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSignOut}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-full transition-colors"
              title="تسجيل الخروج"
            >
              <LogOut className="h-5 w-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-full transition-colors">
              <MoreHorizontal className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      <ComposeModal
        isOpen={isComposeOpen}
        onClose={() => setIsComposeOpen(false)}
        onPost={async (content: string) => {
          // Handle post creation
          setIsComposeOpen(false);
          // Refresh the page or update the feed
          window.location.reload();
        }}
      />
    </div>
  );
}
