import React, { useState, useEffect } from 'react';
import { Send, Search, MoreHorizontal } from 'lucide-react';
import { Layout } from '../components/Layout';
import { supabase, Message, Profile } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface Conversation {
  user: Profile;
  lastMessage: Message;
  unreadCount: number;
}

export function Messages() {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Profile | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  useEffect(() => {
    if (selectedConversation && user) {
      fetchMessages(selectedConversation.id);
    }
  }, [selectedConversation, user]);

  const fetchConversations = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey (
            id,
            username,
            full_name,
            avatar_url,
            verified
          ),
          receiver:profiles!messages_receiver_id_fkey (
            id,
            username,
            full_name,
            avatar_url,
            verified
          )
        `)
        .or(`sender_id.eq.${user.id},receiver_id.eq.${user.id}`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group messages by conversation
      const conversationMap = new Map<string, Conversation>();
      
      data?.forEach((message) => {
        const otherUser = message.sender_id === user.id ? message.receiver : message.sender;
        if (!otherUser) return;

        const existingConv = conversationMap.get(otherUser.id);
        if (!existingConv || new Date(message.created_at) > new Date(existingConv.lastMessage.created_at)) {
          conversationMap.set(otherUser.id, {
            user: otherUser,
            lastMessage: message,
            unreadCount: message.receiver_id === user.id && !message.read ? 1 : 0
          });
        }
      });

      setConversations(Array.from(conversationMap.values()));
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (otherUserId: string) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          ),
          receiver:profiles!messages_receiver_id_fkey (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .or(`and(sender_id.eq.${user.id},receiver_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},receiver_id.eq.${user.id})`)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setMessages(data || []);

      // Mark messages as read
      await supabase
        .from('messages')
        .update({ read: true })
        .eq('sender_id', otherUserId)
        .eq('receiver_id', user.id)
        .eq('read', false);

    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!user || !selectedConversation || !newMessage.trim()) return;

    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          receiver_id: selectedConversation.id,
          content: newMessage.trim()
        });

      if (error) throw error;

      setNewMessage('');
      fetchMessages(selectedConversation.id);
      fetchConversations();
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}د`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}س`;
    return date.toLocaleDateString('ar');
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto border-x border-gray-800 min-h-screen flex">
        {/* Conversations List */}
        <div className="w-1/3 border-r border-gray-800">
          <div className="sticky top-0 bg-black border-b border-gray-800 p-4">
            <h1 className="text-xl font-bold mb-4">الرسائل</h1>
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث في الرسائل"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-900 border border-gray-700 rounded-full py-2 pl-10 pr-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>

          <div className="overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <p>لا توجد محادثات</p>
              </div>
            ) : (
              conversations
                .filter(conv => 
                  !searchQuery || 
                  conv.user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  conv.user.username.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map((conversation) => (
                  <div
                    key={conversation.user.id}
                    onClick={() => setSelectedConversation(conversation.user)}
                    className={`p-4 border-b border-gray-800 hover:bg-gray-900/50 cursor-pointer transition-colors ${
                      selectedConversation?.id === conversation.user.id ? 'bg-gray-900' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      {conversation.user.avatar_url ? (
                        <img
                          src={conversation.user.avatar_url}
                          alt={conversation.user.full_name || conversation.user.username}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-600 rounded-full"></div>
                      )}
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="font-semibold text-white truncate">
                            {conversation.user.full_name || conversation.user.username}
                          </p>
                          <p className="text-gray-500 text-sm">
                            {formatTimeAgo(conversation.lastMessage.created_at)}
                          </p>
                        </div>
                        
                        <p className="text-gray-400 text-sm truncate">
                          {conversation.lastMessage.content}
                        </p>
                      </div>
                      
                      {conversation.unreadCount > 0 && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                ))
            )}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedConversation ? (
            <>
              {/* Chat Header */}
              <div className="sticky top-0 bg-black border-b border-gray-800 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    {selectedConversation.avatar_url ? (
                      <img
                        src={selectedConversation.avatar_url}
                        alt={selectedConversation.full_name || selectedConversation.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-600 rounded-full"></div>
                    )}
                    
                    <div>
                      <p className="font-semibold text-white">
                        {selectedConversation.full_name || selectedConversation.username}
                      </p>
                      <p className="text-gray-400 text-sm">
                        @{selectedConversation.username}
                      </p>
                    </div>
                  </div>
                  
                  <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-full transition-colors">
                    <MoreHorizontal className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        message.sender_id === user?.id
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-white'
                      }`}
                    >
                      <p>{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender_id === user?.id ? 'text-blue-100' : 'text-gray-400'
                      }`}>
                        {formatTimeAgo(message.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="border-t border-gray-800 p-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="text"
                    placeholder="اكتب رسالة..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    className="flex-1 bg-gray-900 border border-gray-700 rounded-full py-2 px-4 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                  />
                  <button
                    onClick={sendMessage}
                    disabled={!newMessage.trim()}
                    className="p-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-full transition-colors"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-400">
              <p>اختر محادثة لبدء المراسلة</p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
