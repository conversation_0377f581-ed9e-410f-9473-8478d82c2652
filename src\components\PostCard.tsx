import React, { useState } from 'react';
import { Heart, MessageCircle, Repeat2, Share, MoreHorizontal, CheckCircle, Bookmark } from 'lucide-react';
import { motion } from 'framer-motion';
import { Post, supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface PostCardProps {
  post: Post;
  onLike?: (postId: string) => void;
  onUnlike?: (postId: string) => void;
}

export function PostCard({ post, onLike, onUnlike }: PostCardProps) {
  const { user } = useAuth();
  const [isLiked, setIsLiked] = useState(post.is_liked || false);
  const [likesCount, setLikesCount] = useState(post.likes_count);
  const [isBookmarked, setIsBookmarked] = useState(false);

  const handleLike = async () => {
    if (!user) return;

    try {
      if (isLiked) {
        // Unlike
        await supabase
          .from('likes')
          .delete()
          .eq('user_id', user.id)
          .eq('post_id', post.id);

        await supabase.rpc('decrement_likes_count', { post_id: post.id });

        setIsLiked(false);
        setLikesCount(prev => Math.max(0, prev - 1));

        if (onUnlike) onUnlike(post.id);
      } else {
        // Like
        await supabase
          .from('likes')
          .insert({
            user_id: user.id,
            post_id: post.id
          });

        await supabase.rpc('increment_likes_count', { post_id: post.id });

        // Create notification
        if (post.user_id !== user.id) {
          await supabase
            .from('notifications')
            .insert({
              user_id: post.user_id,
              type: 'like',
              title: 'إعجاب جديد',
              message: 'أعجب بمنشورك',
              related_post_id: post.id,
              related_user_id: user.id
            });
        }

        setIsLiked(true);
        setLikesCount(prev => prev + 1);

        if (onLike) onLike(post.id);
      }
    } catch (error) {
      console.error('Error handling like:', error);
    }
  };

  const handleBookmark = async () => {
    if (!user) return;

    try {
      if (isBookmarked) {
        await supabase
          .from('bookmarks')
          .delete()
          .eq('user_id', user.id)
          .eq('post_id', post.id);

        setIsBookmarked(false);
      } else {
        await supabase
          .from('bookmarks')
          .insert({
            user_id: user.id,
            post_id: post.id
          });

        setIsBookmarked(true);
      }
    } catch (error) {
      console.error('Error handling bookmark:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d`;
  };
  
  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    return num.toString();
  };

  return (
    <motion.div
      className="border-b border-gray-800 p-4 hover:bg-gray-950/50 transition-colors"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex space-x-3 space-x-reverse">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
          {post.profiles?.avatar_url ? (
            <img
              src={post.profiles.avatar_url}
              alt="Profile"
              className="w-12 h-12 rounded-full"
            />
          ) : (
            <span className="text-lg font-semibold text-white">
              {post.profiles?.username?.[0]?.toUpperCase() || 'U'}
            </span>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <div className="flex items-center space-x-1">
              <span className="font-semibold hover:underline cursor-pointer">{post.profiles?.full_name || post.profiles?.username}</span>
              {post.profiles?.verified && (
                <CheckCircle size={16} className="text-blue-500 fill-current" />
              )}
            </div>
            <span className="text-gray-500">@{post.profiles?.username}</span>
            <span className="text-gray-500">·</span>
            <span className="text-gray-500 hover:underline cursor-pointer">{formatDate(post.created_at)}</span>
            <div className="ml-auto">
              <button className="text-gray-500 hover:text-white p-2 rounded-full hover:bg-gray-800 transition-colors">
                <MoreHorizontal size={16} />
              </button>
            </div>
          </div>

          <div className="mb-3">
            <p className="text-white whitespace-pre-wrap break-words leading-relaxed">{post.content}</p>
            {post.image_url && (
              <img
                src={post.image_url}
                alt="Post image"
                className="mt-3 rounded-2xl max-w-full border border-gray-700"
              />
            )}
          </div>

          <div className="flex items-center justify-between max-w-md">
            <button className="flex items-center space-x-2 space-x-reverse text-gray-500 hover:text-blue-500 p-2 rounded-full hover:bg-blue-500/10 transition-colors group">
              <MessageCircle size={18} className="group-hover:scale-110 transition-transform" />
              <span className="text-sm">{formatNumber(post.replies_count)}</span>
            </button>

            <button className="flex items-center space-x-2 space-x-reverse text-gray-500 hover:text-green-500 p-2 rounded-full hover:bg-green-500/10 transition-colors group">
              <Repeat2 size={18} className="group-hover:scale-110 transition-transform" />
              <span className="text-sm">{formatNumber(post.reposts_count)}</span>
            </button>

            <motion.button
              onClick={handleLike}
              className={`flex items-center space-x-2 space-x-reverse p-2 rounded-full transition-colors group ${
                isLiked
                  ? 'text-red-500'
                  : 'text-gray-500 hover:text-red-500 hover:bg-red-500/10'
              }`}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={isLiked ? { scale: [1, 1.2, 1] } : { scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <Heart size={18} fill={isLiked ? 'currentColor' : 'none'} className="group-hover:scale-110 transition-transform" />
              </motion.div>
              <span className="text-sm">{formatNumber(likesCount)}</span>
            </motion.button>

            <button
              onClick={handleBookmark}
              className={`flex items-center space-x-2 space-x-reverse p-2 rounded-full transition-colors group ${
                isBookmarked
                  ? 'text-blue-500'
                  : 'text-gray-500 hover:text-blue-500 hover:bg-blue-500/10'
              }`}
            >
              <Bookmark size={18} fill={isBookmarked ? 'currentColor' : 'none'} className="group-hover:scale-110 transition-transform" />
            </button>

            <button className="flex items-center space-x-2 space-x-reverse text-gray-500 hover:text-blue-500 p-2 rounded-full hover:bg-blue-500/10 transition-colors group">
              <Share size={18} className="group-hover:scale-110 transition-transform" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
