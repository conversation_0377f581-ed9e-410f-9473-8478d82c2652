import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';

export function Auth() {
  const { signIn, signUp } = useAuth();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showEmailConfirmation, setShowEmailConfirmation] = useState(false);
  const [signupEmail, setSignupEmail] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (isLogin) {
        const { error } = await signIn(email, password);
        if (error) {
          if (error.message === 'Invalid login credentials') {
            setError('Invalid email or password. Please check your credentials and make sure you have confirmed your email address.');
          } else if (error.message === 'Email not confirmed') {
            setError('Please confirm your email address before signing in. Check your inbox for a confirmation link.');
          } else {
            setError(error.message);
          }
        }
      } else {
        if (!username.trim()) {
          throw new Error('Username is required');
        }
        if (!fullName.trim()) {
          throw new Error('Full name is required');
        }
        if (username.length < 3) {
          throw new Error('Username must be at least 3 characters long');
        }
        if (password.length < 6) {
          throw new Error('Password must be at least 6 characters long');
        }

        const { error } = await signUp(email, password, username, fullName);
        if (error) {
          setError(error.message);
        } else {
          // Show email confirmation screen
          setSignupEmail(email);
          setShowEmailConfirmation(true);
          resetForm();
        }
      }
    } catch (error: any) {
      console.error('Auth error:', error);
      setError(error.message || 'An error occurred. Please try again.');
    }
    setLoading(false);
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setUsername('');
    setFullName('');
    setError('');
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setShowEmailConfirmation(false);
    resetForm();
  };

  const backToLogin = () => {
    setShowEmailConfirmation(false);
    setIsLogin(true);
    resetForm();
  };

  // Email confirmation screen
  if (showEmailConfirmation) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-gray-900 rounded-2xl p-8 text-center">
            <div className="mb-6">
              <Mail size={64} className="mx-auto text-blue-500 mb-4" />
              <h2 className="text-2xl font-bold mb-2">Check your email</h2>
              <p className="text-gray-400">
                We've sent a confirmation link to:
              </p>
              <p className="text-blue-400 font-semibold mt-2">{signupEmail}</p>
            </div>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
              <CheckCircle className="mx-auto text-blue-500 mb-2" size={24} />
              <p className="text-sm text-gray-300">
                Click the link in the email to confirm your account, then return here to sign in.
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={backToLogin}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 rounded-lg transition-colors"
              >
                Continue to Sign In
              </button>
              
              <p className="text-xs text-gray-500">
                Didn't receive the email? Check your spam folder or try signing up again.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">Forvgo</h1>
          <p className="text-gray-500">Join the conversation</p>
        </div>

        <div className="bg-gray-900 rounded-2xl p-8">
          <h2 className="text-2xl font-bold mb-6 text-center">
            {isLogin ? 'Sign in to Forvgo' : 'Create your account'}
          </h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            {!isLogin && (
              <>
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
                    placeholder="Enter your full name"
                    required
                    minLength={2}
                  />
                </div>
                <div>
                  <label htmlFor="username" className="block text-sm font-medium mb-2">
                    Username *
                  </label>
                  <input
                    type="text"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, ''))}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
                    placeholder="Choose a username"
                    required
                    minLength={3}
                    maxLength={20}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Only lowercase letters, numbers, and underscores allowed
                  </p>
                </div>
              </>
            )}
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                Email *
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="Enter your email"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-2">
                Password *
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
                placeholder="Enter your password"
                required
                minLength={6}
              />
              {!isLogin && (
                <p className="text-xs text-gray-500 mt-1">
                  Must be at least 6 characters long
                </p>
              )}
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="text-red-400 flex-shrink-0 mt-0.5" size={16} />
                  <p className="text-red-400 text-sm">{error}</p>
                </div>
              </div>
            )}

            {isLogin && (
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <Mail className="text-blue-400 flex-shrink-0 mt-0.5" size={16} />
                  <p className="text-blue-400 text-sm">
                    Make sure you've confirmed your email address before signing in.
                  </p>
                </div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 disabled:cursor-not-allowed text-white font-bold py-3 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : (isLogin ? 'Sign in' : 'Create Account')}
            </button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={toggleMode}
              className="text-blue-500 hover:underline transition-colors"
            >
              {isLogin ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
            </button>
          </div>

          {isLogin && (
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                New to Forvgo? Create an account to get started!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
