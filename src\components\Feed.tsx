import React, { useState, useEffect } from 'react';
import { supabase, Post } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { PostCard } from './PostCard';
import { ComposeModal } from './ComposeModal';
import { Layout } from './Layout';

export function Feed() {
  const { user } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [isComposeOpen, setIsComposeOpen] = useState(false);

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id (
            id,
            username,
            full_name,
            avatar_url,
            verified
          ),
          likes!left (
            user_id
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const postsWithLikes = data?.map(post => ({
        ...post,
        is_liked: post.likes?.some((like: any) => like.user_id === user?.id) || false
      })) || [];

      setPosts(postsWithLikes);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePost = async (content: string) => {
    if (!user) return;

    const { error } = await supabase
      .from('posts')
      .insert([
        {
          user_id: user.id,
          content,
          likes_count: 0,
          reposts_count: 0,
          replies_count: 0,
        },
      ]);

    if (error) {
      console.error('Error creating post:', error);
      throw error;
    }

    if (user) {
      await supabase.rpc('increment_posts_count', { user_id: user.id });
    }

    await fetchPosts();
  };

  const handleLike = async (postId: string) => {
    if (!user) return;

    const { error } = await supabase
      .from('likes')
      .insert([
        {
          user_id: user.id,
          post_id: postId,
        },
      ]);

    if (!error) {
      await supabase.rpc('increment_likes_count', { post_id: postId });
      // No full refetch, just update local state for better UX
      setPosts(posts.map(p => p.id === postId ? { ...p, likes_count: p.likes_count + 1, is_liked: true } : p));
    } else {
        console.error('Error liking post:', error);
    }
  };

  const handleUnlike = async (postId: string) => {
    if (!user) return;

    const { error } = await supabase
      .from('likes')
      .delete()
      .eq('user_id', user.id)
      .eq('post_id', postId);

    if (!error) {
      await supabase.rpc('decrement_likes_count', { post_id: postId });
      // No full refetch, just update local state for better UX
      setPosts(posts.map(p => p.id === postId ? { ...p, likes_count: p.likes_count - 1, is_liked: false } : p));
    } else {
        console.error('Error unliking post:', error);
    }
  };

  return (
    <Layout onComposeClick={() => setIsComposeOpen(true)}>
      <div className="border-x border-gray-800 min-h-screen">
        <div className="sticky top-0 bg-black/80 backdrop-blur-lg border-b border-gray-800 p-4">
          <h1 className="text-xl font-bold">Home</h1>
        </div>

        {posts.length > 0 && posts.filter(p => p.user_id === user?.id).length === 0 && (
          <div className="border-b border-gray-800 p-6 bg-gray-950/50">
            <h2 className="text-lg font-semibold mb-2">Welcome to Forvgo! 🎉</h2>
            <p className="text-gray-400 mb-4">
              Start by creating your first post and joining the conversation.
            </p>
            <button
              onClick={() => setIsComposeOpen(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full transition-colors"
            >
              Create your first post
            </button>
          </div>
        )}

        <div>
          {loading ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              <p className="mb-4">The feed is empty. Be the first to post!</p>
               <button
                onClick={() => setIsComposeOpen(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full transition-colors"
              >
                Create Post
              </button>
            </div>
          ) : (
            posts.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onLike={handleLike}
                onUnlike={handleUnlike}
              />
            ))
          )}
        </div>
      </div>

      <ComposeModal
        isOpen={isComposeOpen}
        onClose={() => setIsComposeOpen(false)}
        onPost={handleCreatePost}
      />
    </Layout>
  );
}
