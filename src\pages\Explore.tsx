import React, { useState, useEffect } from 'react';
import { Search, TrendingUp } from 'lucide-react';
import { Layout } from '../components/Layout';
import { PostCard } from '../components/PostCard';
import { supabase, Post } from '../lib/supabase';

export function Explore() {
  const [searchQuery, setSearchQuery] = useState('');
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'trending' | 'latest'>('trending');

  useEffect(() => {
    fetchPosts();
  }, [activeTab]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('posts')
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            verified
          )
        `);

      if (activeTab === 'trending') {
        query = query.order('likes_count', { ascending: false });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      const { data, error } = await query.limit(20);

      if (error) throw error;
      setPosts(data || []);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      fetchPosts();
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            verified
          )
        `)
        .textSearch('content', searchQuery)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPosts(data || []);
    } catch (error) {
      console.error('Error searching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const trendingTopics = [
    '#تقنية',
    '#برمجة',
    '#ذكي_اصطناعي',
    '#تطوير_ويب',
    '#موبايل',
    '#تصميم',
    '#ريادة_أعمال',
    '#تسويق',
  ];

  return (
    <Layout>
      <div className="max-w-2xl mx-auto border-x border-gray-800 min-h-screen">
        {/* Header */}
        <div className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-gray-800 p-4">
          <h1 className="text-xl font-bold mb-4">استكشاف</h1>
          
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="البحث في Forvgo"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full bg-gray-900 border border-gray-700 rounded-full py-3 pl-12 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>

          {/* Tabs */}
          <div className="flex space-x-4 space-x-reverse">
            <button
              onClick={() => setActiveTab('trending')}
              className={`px-4 py-2 rounded-full transition-colors ${
                activeTab === 'trending'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              <TrendingUp className="inline h-4 w-4 ml-2" />
              الأكثر شيوعاً
            </button>
            <button
              onClick={() => setActiveTab('latest')}
              className={`px-4 py-2 rounded-full transition-colors ${
                activeTab === 'latest'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800'
              }`}
            >
              الأحدث
            </button>
          </div>
        </div>

        {/* Trending Topics */}
        <div className="p-4 border-b border-gray-800">
          <h2 className="text-lg font-bold mb-3">المواضيع الشائعة</h2>
          <div className="flex flex-wrap gap-2">
            {trendingTopics.map((topic) => (
              <button
                key={topic}
                onClick={() => {
                  setSearchQuery(topic);
                  handleSearch();
                }}
                className="bg-gray-800 hover:bg-gray-700 text-blue-400 px-3 py-1 rounded-full text-sm transition-colors"
              >
                {topic}
              </button>
            ))}
          </div>
        </div>

        {/* Posts */}
        <div>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <p>لا توجد منشورات للعرض</p>
            </div>
          ) : (
            posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))
          )}
        </div>
      </div>
    </Layout>
  );
}
